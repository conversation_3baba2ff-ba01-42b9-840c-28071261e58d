/* Reset y Variables CSS */
:root {
    /* Paleta de Colores */
    --azul-marino: #0A1128;
    --gris-grafito: #2C3E50;
    --cian-electrico: #00BCD4;
    --amarillo-brillante: #FFD700;
    --blanco-puro: #FFFFFF;

    /* Gradientes */
    --gradient-primary: linear-gradient(135deg, var(--azul-marino) 0%, var(--gris-grafito) 100%);
    --gradient-accent: linear-gradient(135deg, var(--cian-electrico) 0%, #00ACC1 100%);
    --gradient-warm: linear-gradient(135deg, var(--amarillo-brillante) 0%, #FFC107 100%);

    /* Sombras */
    --shadow-soft: 0 4px 20px rgba(10, 17, 40, 0.1);
    --shadow-medium: 0 8px 30px rgba(10, 17, 40, 0.15);
    --shadow-strong: 0 15px 40px rgba(10, 17, 40, 0.2);

    /* Transiciones */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--azul-marino);
    background: var(--blanco-puro);
    overflow-x: hidden;
}

/* Header Mejorado */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 188, 212, 0.1);
    transition: var(--transition-smooth);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--azul-marino);
    text-decoration: none;
    position: relative;
}

.logo::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--gradient-accent);
    transition: var(--transition-smooth);
    border-radius: 2px;
}

.logo:hover::after {
    width: 100%;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--gris-grafito);
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
}

.nav-links a::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: var(--cian-electrico);
    border-radius: 50%;
    opacity: 0;
    transition: var(--transition-smooth);
}

.nav-links a:hover {
    color: var(--cian-electrico);
    transform: translateY(-2px);
}

.nav-links a:hover::before {
    opacity: 1;
    transform: translateX(-50%) translateY(-2px);
}

.nav-cta {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.welcome-user {
    color: var(--gris-grafito);
    font-weight: 500;
}

/* Botones Rediseñados */
.btn {
    padding: 12px 24px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--blanco-puro);
    box-shadow: var(--shadow-soft);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 188, 212, 0.2);
}

.btn-ghost {
    background: transparent;
    color: var(--cian-electrico);
    border: 2px solid var(--cian-electrico);
    box-shadow: none;
}

.btn-ghost:hover {
    background: var(--cian-electrico);
    color: var(--blanco-puro);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 188, 212, 0.15);
}

.btn-white {
    background: var(--blanco-puro);
    color: var(--azul-marino);
    box-shadow: var(--shadow-soft);
    border: 2px solid transparent;
}

.btn-white:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(10, 17, 40, 0.15);
}

/* Hero Section Rediseñado */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 6rem 2rem 2rem;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 600px;
    height: 600px;
    background: var(--cian-electrico);
    border-radius: 50%;
    opacity: 0.1;
    animation: float 20s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -5%;
    width: 400px;
    height: 400px;
    background: var(--amarillo-brillante);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    opacity: 0.1;
    animation: float 15s ease-in-out infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content > div:first-child {
    order: 1;
}

.hero-visual {
    order: 2;
}

.hero-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--blanco-puro);
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-content p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Mockup Mejorado */
.hero-visual {
    position: relative;
}

.mockup {
    background: var(--blanco-puro);
    border-radius: 24px;
    padding: 2rem;
    box-shadow: var(--shadow-strong);
    position: relative;
    overflow: hidden;
}

.mockup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
}

.chat-interface {
    space-y: 1rem;
}

.model-selector {
    background: var(--gradient-accent);
    color: var(--blanco-puro);
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1.5rem;
}

.message {
    padding: 16px 20px;
    border-radius: 16px;
    margin-bottom: 1rem;
    position: relative;
}

.message.user {
    background: linear-gradient(135deg, var(--amarillo-brillante), #FFC107);
    color: var(--azul-marino);
    margin-left: 2rem;
}

.message.ai {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    color: var(--gris-grafito);
    margin-right: 2rem;
}

/* Features Section Rediseñado */
.features {
    padding: 6rem 2rem;
    background: linear-gradient(180deg, var(--blanco-puro) 0%, #f8fafc 100%);
    position: relative;
}

.section-header {
    text-align: center;
    max-width: 600px;
    margin: 0 auto 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--azul-marino);
    margin-bottom: 1rem;
}

.feature-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 3rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    background: var(--blanco-puro);
    border-radius: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: var(--transition-smooth);
}

.feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(10, 17, 40, 0.12);
}

.feature:hover::before {
    transform: scaleX(1);
}

.feature-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--azul-marino);
    margin-bottom: 1rem;
}

.feature-content p {
    color: var(--gris-grafito);
    line-height: 1.6;
}

.feature-visual {
    flex-shrink: 0;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--blanco-puro);
    position: relative;
    overflow: hidden;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: spin 3s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Formas Geométricas Únicas por Feature */
.feature:nth-child(1) .feature-icon {
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    background: var(--gradient-accent);
}

.feature:nth-child(2) .feature-icon {
    border-radius: 50% 10px 50% 10px;
    background: var(--gradient-warm);
}

.feature:nth-child(3) .feature-icon {
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    background: var(--gradient-primary);
}

.feature:nth-child(4) .feature-icon {
    border-radius: 20px;
    background: var(--gradient-accent);
    transform: rotate(45deg);
}

.feature:nth-child(4) .feature-icon::after {
    content: '⚡';
    transform: rotate(-45deg);
}

/* Mathematical Capabilities Section */
.math-section {
    padding: 6rem 2rem;
    background: linear-gradient(180deg, #f8fafc 0%, var(--blanco-puro) 100%);
    position: relative;
    overflow: hidden;
}

.math-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: -20%;
    width: 400px;
    height: 400px;
    background: var(--cian-electrico);
    border-radius: 50%;
    opacity: 0.03;
    animation: float 25s ease-in-out infinite;
}

.math-container {
    max-width: 1200px;
    margin: 0 auto;
}

.math-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.math-card {
    background: var(--blanco-puro);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.math-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
}

.math-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(10, 17, 40, 0.1);
}

.math-visual {
    margin-bottom: 1.5rem;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    position: relative;
}

.equation-display {
    text-align: center;
}

.equation {
    font-family: 'Times New Roman', serif;
    font-size: 1.2rem;
    color: var(--azul-marino);
    margin: 0.5rem 0;
    font-weight: 500;
}

.chart-display {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: end;
    justify-content: center;
    gap: 8px;
}

.chart-bars {
    display: flex;
    align-items: end;
    gap: 6px;
    height: 60px;
}

.bar {
    width: 12px;
    background: var(--gradient-accent);
    border-radius: 2px 2px 0 0;
    animation: growBar 2s ease-out;
}

@keyframes growBar {
    from { height: 0; }
    to { height: var(--final-height, 100%); }
}

.matrix-display {
    text-align: center;
}

.matrix {
    font-family: 'Times New Roman', serif;
    font-size: 1.1rem;
    color: var(--azul-marino);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.symbols {
    font-size: 1.5rem;
    color: var(--cian-electrico);
    letter-spacing: 0.5rem;
}

.math-content h3 {
    color: var(--azul-marino);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.math-content p {
    color: var(--gris-grafito);
    line-height: 1.6;
}

/* Global Reach Section */
.global-section {
    padding: 6rem 2rem;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.global-container {
    max-width: 1200px;
    margin: 0 auto;
}

.global-section .section-header h2 {
    color: var(--blanco-puro);
}

.global-section .section-header p {
    color: rgba(255, 255, 255, 0.9);
}

.global-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 3rem;
}

.world-map {
    position: relative;
    height: 400px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.map-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 500"><path d="M150,200 Q200,150 300,180 T500,200 Q600,220 700,200 T900,180" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/><path d="M100,300 Q200,280 350,300 T600,320 Q750,340 850,320" stroke="rgba(255,255,255,0.2)" stroke-width="2" fill="none"/></svg>') center/cover no-repeat;
}

.continent {
    position: absolute;
}

.north-america { top: 25%; left: 20%; }
.europe { top: 20%; left: 50%; }
.asia { top: 25%; left: 70%; }
.south-america { top: 60%; left: 30%; }
.africa { top: 50%; left: 52%; }
.oceania { top: 70%; left: 80%; }

.pin {
    width: 20px;
    height: 20px;
    background: var(--amarillo-brillante);
    border-radius: 50%;
    position: relative;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.pin.active {
    background: var(--cian-electrico);
}

.pin:hover {
    transform: scale(1.2);
}

.pulse {
    position: absolute;
    top: -5px;
    left: -5px;
    width: 30px;
    height: 30px;
    border: 2px solid var(--cian-electrico);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.global-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition-smooth);
}

.stat-card:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--amarillo-brillante);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--blanco-puro);
    font-weight: 500;
}

/* Testimonials Rediseñado */
.testimonials {
    padding: 6rem 2rem;
    background: var(--gradient-primary);
    position: relative;
}

.testimonials-container {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.testimonials h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--blanco-puro);
    margin-bottom: 3rem;
}

.testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial {
    background: var(--blanco-puro);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow-medium);
    position: relative;
    transition: var(--transition-smooth);
}

.testimonial::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: var(--cian-electrico);
    font-weight: 700;
}

.testimonial:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(10, 17, 40, 0.15);
}

.testimonial p {
    color: var(--gris-grafito);
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.testimonial-author {
    color: var(--azul-marino);
    font-weight: 600;
}

/* Use Cases Section */
.use-cases-section {
    padding: 6rem 2rem;
    background: linear-gradient(180deg, var(--blanco-puro) 0%, #f8fafc 100%);
    position: relative;
}

.use-cases-section::before {
    content: '';
    position: absolute;
    bottom: -10%;
    left: -10%;
    width: 300px;
    height: 300px;
    background: var(--amarillo-brillante);
    border-radius: 50%;
    opacity: 0.05;
    animation: float 20s ease-in-out infinite reverse;
}

.use-cases-container {
    max-width: 1200px;
    margin: 0 auto;
}

.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.use-case {
    background: var(--blanco-puro);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow-soft);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.use-case::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-warm);
    transform: scaleX(0);
    transition: var(--transition-smooth);
}

.use-case:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(10, 17, 40, 0.12);
}

.use-case:hover::before {
    transform: scaleX(1);
}

.use-case-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
}

.use-case h3 {
    color: var(--azul-marino);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.use-case p {
    color: var(--gris-grafito);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.use-case-metrics {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(135deg, var(--cian-electrico), #00ACC1);
    color: var(--blanco-puro);
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    display: inline-block;
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
    .global-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .world-map {
        height: 300px;
    }

    .global-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}

/* CTA Section */
.cta-section {
    padding: 6rem 2rem;
    background: var(--gradient-accent);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    background: var(--amarillo-brillante);
    border-radius: 50%;
    opacity: 0.1;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--blanco-puro);
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

/* Footer */
footer {
    background: var(--azul-marino);
    color: var(--blanco-puro);
    padding: 3rem 2rem 2rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h3 {
    color: var(--cian-electrico);
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-smooth);
}

.footer-section ul li a:hover {
    color: var(--cian-electrico);
    transform: translateX(5px);
}

/* Decorative Elements */
.section-header::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.section-header {
    position: relative;
}

/* Enhanced Visual Elements */
.math-visual::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background: var(--cian-electrico);
    border-radius: 50%;
    opacity: 0.3;
}

.use-case::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: -10px;
    width: 40px;
    height: 40px;
    background: var(--gradient-warm);
    border-radius: 50%;
    opacity: 0.1;
}

/* Improved Typography */
.section-header h2 {
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50%;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

/* Enhanced Hover States */
.math-card:hover .equation {
    color: var(--cian-electrico);
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.math-card:hover .symbols {
    animation: symbolPulse 1s ease-in-out;
}

@keyframes symbolPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.use-case:hover .use-case-icon {
    transform: scale(1.1) rotate(5deg);
    transition: all 0.3s ease;
}

/* Loading States for Interactive Elements */
.pin::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: var(--blanco-puro);
    border-radius: 50%;
    opacity: 0.8;
}

/* Gradient Text Effects */
.stat-number {
    background: var(--gradient-warm);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Animaciones */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered Animation Delays */
.math-card:nth-child(1) { animation-delay: 0.1s; }
.math-card:nth-child(2) { animation-delay: 0.2s; }
.math-card:nth-child(3) { animation-delay: 0.3s; }

.use-case:nth-child(1) { animation-delay: 0.1s; }
.use-case:nth-child(2) { animation-delay: 0.2s; }
.use-case:nth-child(3) { animation-delay: 0.3s; }
.use-case:nth-child(4) { animation-delay: 0.4s; }
.use-case:nth-child(5) { animation-delay: 0.5s; }
.use-case:nth-child(6) { animation-delay: 0.6s; }

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .feature {
        flex-direction: column;
        text-align: center;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .math-grid {
        grid-template-columns: 1fr;
    }

    .use-cases-grid {
        grid-template-columns: 1fr;
    }

    .global-stats {
        grid-template-columns: 1fr 1fr;
    }

    .nav-links {
        display: none;
    }

    nav {
        padding: 1rem;
    }

    .equation {
        font-size: 1rem;
    }

    .symbols {
        font-size: 1.2rem;
        letter-spacing: 0.3rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 4rem 1rem 2rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .features,
    .testimonials,
    .cta-section,
    .math-section,
    .global-section,
    .use-cases-section {
        padding: 4rem 1rem;
    }

    .math-card,
    .use-case {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .use-case-icon {
        font-size: 2.5rem;
    }

    .world-map {
        height: 250px;
    }

    .global-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
