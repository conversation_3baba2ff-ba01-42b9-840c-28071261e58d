{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agora - Tu Espacio de Trabajo de IA Unificado</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'chat/css/home.css' %}">
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <a href="{% url 'home' %}" class="logo">Agora</a>
            <ul class="nav-links">
                <li><a href="#features">Características</a></li>
                <li><a href="#pricing">Precios</a></li>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#about">Acerca de</a></li>
                <li><a href="#support">Soporte</a></li>
            </ul>
            <div class="nav-cta">
                {% if user.is_authenticated %}
                    <span class="welcome-user">Hola, {{ user.username }}</span>
                    <form action="{% url 'logout' %}" method="POST" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-ghost">Cerrar Sesión</button>
                    </form>
                    <a href="{% url 'chat' %}" class="btn btn-primary">Ir al Chat</a>
                {% else %}
                    <a href="{% url 'login' %}" class="btn btn-ghost">Iniciar Sesión</a>
                    <a href="{% url 'register' %}" class="btn btn-primary">Comenzar Gratis</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Libera el Poder de Cada LLM</h1>
                    <p>Conecta, Crea y Aprende con Múltiples Modelos de IA, Notas y Agentes, Todo en Una Interfaz Intuitiva.</p>
                    <div class="hero-cta">
                        <a href="{% if user.is_authenticated %}{% url 'chat' %}{% else %}{% url 'register' %}{% endif %}" class="btn btn-primary">Comenzar Gratis</a>
                        <a href="#features" class="btn btn-ghost">Explorar Características</a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="mockup">
                        <div class="chat-interface">
                            <div class="model-selector">
                                🤖 GPT-4 • Claude • Gemini
                            </div>
                            <div class="message user">¿Cómo puedo optimizar mi flujo de trabajo con IA?</div>
                            <div class="message ai">¡Puedo ayudarte a optimizar tu flujo de trabajo! Déjame analizar tus procesos actuales y sugerir mejoras impulsadas por IA...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

            <!-- Use Cases Section -->
    <section class="use-cases-section">
        <div class="use-cases-container">
            <div class="section-header">
                <h2>Casos de Uso Transformadores</h2>
            </div>
            <div class="use-cases-grid">
                <div class="use-case animate-on-scroll">
                    <div class="use-case-icon">🔬</div>
                    <h3>Investigación Científica</h3>
                    <p>Acelera el descubrimiento científico con análisis de datos avanzado, modelado predictivo y revisión automatizada de literatura.</p>
                    <div class="use-case-metrics">
                        <span>+300% velocidad de análisis</span>
                    </div>
                </div>
                <div class="use-case animate-on-scroll">
                    <div class="use-case-icon">🎓</div>
                    <h3>Educación Personalizada</h3>
                    <p>Crea experiencias de aprendizaje adaptativas que se ajustan al ritmo y estilo de cada estudiante.</p>
                    <div class="use-case-metrics">
                        <span>+85% retención de conocimiento</span>
                    </div>
                </div>
                <div class="use-case animate-on-scroll">
                    <div class="use-case-icon">🎨</div>
                    <h3>Creatividad Digital</h3>
                    <p>Desde escritura creativa hasta diseño gráfico, potencia tu creatividad con asistencia inteligente.</p>
                    <div class="use-case-metrics">
                        <span>+200% productividad creativa</span>
                    </div>
                </div>
            </div>
        </div>
    </section>-

    <!-- Mathematical Capabilities Section -->
    <section class="math-section">
        <div class="math-container">
            <div class="section-header">
                <h2>Potencia Matemática Avanzada</h2>
            </div>
            <div class="math-grid">
                <div class="math-card animate-on-scroll">
                    <div class="math-visual">
                        <div class="equation-display">
                            <div class="equation">∫₀^∞ e^(-x²) dx = √π/2</div>
                            <div class="equation">∇²φ = ρ/ε₀</div>
                            <div class="equation">E = mc²</div>
                        </div>
                    </div>
                    <div class="math-content">
                        <h3>Cálculo y Análisis</h3>
                        <p>Desde integrales complejas hasta ecuaciones diferenciales, nuestra IA maneja matemáticas avanzadas con precisión y claridad.</p>
                    </div>
                </div>
                <div class="math-card animate-on-scroll">
                    <div class="math-visual">
                        <div class="chart-display">
                            <div class="chart-bars">
                                <div class="bar" style="height: 60%"></div>
                                <div class="bar" style="height: 80%"></div>
                                <div class="bar" style="height: 45%"></div>
                                <div class="bar" style="height: 90%"></div>
                                <div class="bar" style="height: 70%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="math-content">
                        <h3>Visualización de Datos</h3>
                        <p>Transforma números en insights visuales. Crea gráficos, diagramas y representaciones que hacen los datos comprensibles.</p>
                    </div>
                </div>
                <div class="math-card animate-on-scroll">
                    <div class="math-visual">
                        <div class="matrix-display">
                            <div class="matrix">
                                [<span>a₁₁ a₁₂</span>]
                                [<span>a₂₁ a₂₂</span>]
                            </div>
                            <div class="symbols">∑ ∏ ∆ ∇ ∞</div>
                        </div>
                    </div>
                    <div class="math-content">
                        <h3>Álgebra Lineal</h3>
                        <p>Matrices, vectores y transformaciones lineales. Resuelve sistemas complejos con facilidad y comprende los conceptos fundamentales.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

        <!-- Features Section -->
    <section class="features" id="features">
        <div class="section-header">
            <h2>Tu Compañero de IA Todo-en-Uno</h2>
        </div>
        <div class="feature-grid">
            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Conecta con Cada LLM, Sin Problemas</h3>
                    <p>Accede, compara y cambia entre modelos de lenguaje líderes como GPT-4, Claude y Gemini dentro de una sola interfaz optimizada. No más pestañas o cuentas múltiples.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">🔗</div>
                </div>
            </div>

            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Captura Ideas, Organiza Conocimiento</h3>
                    <p>Transforma conversaciones en insights accionables. Nuestras herramientas integradas de toma de notas y creación de cuadernos te ayudan a guardar fragmentos, estructurar pensamientos y construir una base de conocimiento personal junto a tus interacciones de IA.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">📝</div>
                </div>
            </div>

            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Aprende, Experimenta, Innova</h3>
                    <p>Aprovecha la IA para una comprensión más profunda con pruebas interactivas para solidificar conceptos y un poderoso sistema multi-agente para simular escenarios complejos y explorar capacidades avanzadas de IA.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">🧠</div>
                </div>
            </div>

            <div class="feature animate-on-scroll">
                <div class="feature-content">
                    <h3>Diseño Intuitivo, Resultados Poderosos</h3>
                    <p>Experimenta una interfaz minimalista y libre de distracciones diseñada para la eficiencia. Enfócate en tus ideas, no en las herramientas, con respuestas ultrarrápidas y una experiencia de usuario fluida.</p>
                </div>
                <div class="feature-visual">
                    <div class="feature-icon">⚡</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Global Reach Section -->
    <section class="global-section">
        <div class="global-container">
            <div class="section-header">
                <h2>Alcance Global, Impacto Local</h2>
            </div>
            <div class="global-content">
                <div class="world-map">
                    <div class="map-container">
                        <div class="continent north-america">
                            <div class="pin active" data-region="América del Norte">
                                <div class="pulse"></div>
                            </div>
                        </div>
                        <div class="continent europe">
                            <div class="pin active" data-region="Europa">
                                <div class="pulse"></div>
                            </div>
                        </div>
                        <div class="continent asia">
                            <div class="pin active" data-region="Asia">
                                <div class="pulse"></div>
                            </div>
                        </div>
                        <div class="continent south-america">
                            <div class="pin" data-region="América del Sur">
                                <div class="pulse"></div>
                            </div>
                        </div>
                        <div class="continent africa">
                            <div class="pin" data-region="África">
                                <div class="pulse"></div>
                            </div>
                        </div>
                        <div class="continent oceania">
                            <div class="pin" data-region="Oceanía">
                                <div class="pulse"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="global-stats">
                    <div class="stat-card animate-on-scroll">
                        <div class="stat-number">150+</div>
                        <div class="stat-label">Países</div>
                    </div>
                    <div class="stat-card animate-on-scroll">
                        <div class="stat-number">1M+</div>
                        <div class="stat-label">Usuarios Activos</div>
                    </div>
                    <div class="stat-card animate-on-scroll">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Idiomas</div>
                    </div>
                    <div class="stat-card animate-on-scroll">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Disponibilidad</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

        <!-- Testimonials -->
    <section class="testimonials">
        <div class="testimonials-container">
            <h2>Lo Que Dicen Nuestros Usuarios</h2>
            <div class="testimonial-grid">
                <div class="testimonial animate-on-scroll">
                    <p>"Esta aplicación ha cambiado completamente cómo interactúo con los LLMs. ¡La toma de notas es revolucionaria!"</p>
                    <div class="testimonial-author">Sarah Chen, Gerente de Producto</div>
                </div>
                <div class="testimonial animate-on-scroll">
                    <p>"Finalmente, un lugar para todas mis necesidades de IA. El sistema multi-agente es increíblemente poderoso para investigación."</p>
                    <div class="testimonial-author">Dr. Michael Rodríguez, Investigador</div>
                </div>
                <div class="testimonial animate-on-scroll">
                    <p>"El cambio fluido entre modelos ha optimizado dramáticamente mi flujo de trabajo de creación de contenido."</p>
                    <div class="testimonial-author">Emma Thompson, Creadora de Contenido</div>
                </div>
            </div>
        </div>
    </section>

        <!-- CTA Section -->
        <section class="cta-section">
            <div class="cta-content">
                <h2>Experimenta el Futuro de la Interacción con IA</h2>
                <p>Únete a miles de usuarios que están revolucionando su productividad y aprendizaje con Agora.</p>
                <a href="{% if user.is_authenticated %}{% url 'chat' %}{% else %}{% url 'register' %}{% endif %}" class="btn btn-white">Comenzar Gratis Hoy</a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <div class="logo" style="color: white; margin-bottom: 1rem;">Agora</div>
                <p style="color: #9ca3af; margin-bottom: 1rem;">Tu Espacio de Trabajo de IA Unificado</p>
                <p style="color: #6b7280; font-size: 0.9rem;">© 2025 Agora Inc. Todos los derechos reservados.</p>
            </div>
            <div class="footer-section">
                <h3>Producto</h3>
                <ul>
                    <li><a href="#features">Características</a></li>
                    <li><a href="#pricing">Precios</a></li>
                    <li><a href="#integrations">Integraciones</a></li>
                    <li><a href="#roadmap">Hoja de Ruta</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Recursos</h3>
                <ul>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#documentation">Documentación</a></li>
                    <li><a href="#help">Centro de Ayuda</a></li>
                    <li><a href="#api">API</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Empresa</h3>
                <ul>
                    <li><a href="#about">Acerca de Nosotros</a></li>
                    <li><a href="#careers">Carreras</a></li>
                    <li><a href="#contact">Contacto</a></li>
                    <li><a href="#privacy">Política de Privacidad</a></li>
                    <li><a href="#terms">Términos de Servicio</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });

        // Header background change on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Interactive map pins
        document.querySelectorAll('.pin').forEach(pin => {
            pin.addEventListener('mouseenter', function() {
                const region = this.getAttribute('data-region');
                // Create tooltip
                const tooltip = document.createElement('div');
                tooltip.className = 'map-tooltip';
                tooltip.textContent = region;
                tooltip.style.cssText = `
                    position: absolute;
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 8px;
                    font-size: 0.9rem;
                    pointer-events: none;
                    z-index: 1000;
                    transform: translate(-50%, -100%);
                    margin-top: -10px;
                    white-space: nowrap;
                `;
                this.appendChild(tooltip);
            });

            pin.addEventListener('mouseleave', function() {
                const tooltip = this.querySelector('.map-tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });

        // Animate chart bars when in view
        const chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const bars = entry.target.querySelectorAll('.bar');
                    bars.forEach((bar, index) => {
                        setTimeout(() => {
                            bar.style.animation = 'growBar 1s ease-out forwards';
                        }, index * 200);
                    });
                }
            });
        }, { threshold: 0.5 });

        document.querySelectorAll('.chart-display').forEach(chart => {
            chartObserver.observe(chart);
        });

        // Counter animation for stats
        const animateCounter = (element, target) => {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (target >= 1000000) {
                    element.textContent = (current / 1000000).toFixed(1) + 'M+';
                } else if (target >= 1000) {
                    element.textContent = (current / 1000).toFixed(0) + 'K+';
                } else {
                    element.textContent = Math.floor(current) + '+';
                }
            }, 20);
        };

        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumber = entry.target.querySelector('.stat-number');
                    const text = statNumber.textContent;

                    if (text.includes('1M+')) {
                        animateCounter(statNumber, 1000000);
                    } else if (text.includes('150+')) {
                        animateCounter(statNumber, 150);
                    } else if (text.includes('50+')) {
                        animateCounter(statNumber, 50);
                    }

                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        document.querySelectorAll('.stat-card').forEach(card => {
            statsObserver.observe(card);
        });
    </script>
</body>
</html>
